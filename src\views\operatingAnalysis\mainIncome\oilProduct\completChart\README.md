# CompleteChart 组件使用说明

## 功能描述
CompleteChart 是一个重叠柱状图组件，用于显示实际值与预期值的对比。每个柱子包含两个数值：
- **深色部分**：表示实际值（actual value）
- **浅色部分**：表示预期值（expected value），作为背景显示

## 数据结构

```javascript
chartData: [
  { name: "项目名称", actual: 实际值, expected: 预期值 },
  // 更多数据项...
]
```

## 使用示例

```vue
<template>
  <div style="width: 100%; height: 400px;">
    <complete-chart ref="chartRef" />
  </div>
</template>

<script>
import CompleteChart from './completChart/index.vue'

export default {
  components: {
    CompleteChart
  },
  mounted() {
    // 动态更新数据
    const newData = [
      { name: "项目A", actual: 80, expected: 100 },
      { name: "项目B", actual: 65, expected: 80 },
      { name: "项目C", actual: 90, expected: 95 }
    ];
    
    this.$refs.chartRef.updateChartData(newData);
  }
}
</script>
```

## 主要特性

1. **重叠柱状图效果**：浅色背景显示预期值，深色前景显示实际值
2. **智能提示**：鼠标悬停显示详细信息，包括完成率计算
3. **响应式设计**：自动适应容器大小变化
4. **图例显示**：清晰标识实际值和预期值
5. **颜色方案**：
   - 预期值：浅蓝色 `rgba(64, 158, 255, 0.3)`
   - 实际值：深蓝色 `#409EFF`

## API 方法

### updateChartData(newData)
更新图表数据
- **参数**：`newData` - 新的数据数组
- **类型**：`Array<{name: string, actual: number, expected: number}>`

### getCompletionRate(actual, expected)
计算完成率百分比
- **参数**：
  - `actual` - 实际值
  - `expected` - 预期值
- **返回值**：完成率百分比（0-100）

## 样式定制

组件使用 SCSS 编写样式，可以通过以下方式自定义：

```scss
.complete-chart {
  .chart-box {
    min-height: 200px;
    max-height: 280px;
  }
}
```

## 依赖项

- ECharts 5.x
- Vue 2.x

## 注意事项

1. 确保容器有明确的高度设置
2. 实际值应该小于等于预期值以获得最佳视觉效果
3. 数据更新后会自动重新渲染图表
