# CompleteChart 行级高亮效果说明

## 新增功能
为重叠柱状图添加了鼠标悬浮时的行级高亮效果，当鼠标悬浮到某个柱状图时，整个该行（包括Y轴标签区域）会显示背景高亮效果。

## 行级高亮实现

### Graphic组件配置
```javascript
// 创建行高亮背景的graphic元素
const createRowHighlight = (dataLength) => {
  const graphics = [];
  for (let i = 0; i < dataLength; i++) {
    graphics.push({
      type: 'rect',
      id: `rowHighlight_${i}`,
      invisible: true, // 初始状态不可见
      shape: {
        x: 0,
        y: 0,
        width: 0,
        height: 0
      },
      style: {
        fill: 'rgba(64, 158, 255, 0.1)', // 半透明蓝色背景
        stroke: 'rgba(64, 158, 255, 0.3)',
        lineWidth: 1
      },
      z: 0 // 确保在柱子后面
    });
  }
  return graphics;
};
```

### 鼠标事件监听
```javascript
// 鼠标悬浮事件
this.mychart.on('mouseover', 'series', (params) => {
  this.showRowHighlight(params.dataIndex);
});

// 鼠标离开事件
this.mychart.on('mouseout', 'series', () => {
  this.hideRowHighlight();
});

// 鼠标离开图表区域时也隐藏高亮
this.mychart.on('globalout', () => {
  this.hideRowHighlight();
});
```

## 动画配置
```javascript
animation: true,
animationDuration: 300,
animationEasing: 'cubicOut'
```

## 提示框优化
- 深色背景：`rgba(50, 50, 50, 0.9)`
- 蓝色边框：`#409EFF`
- 白色文字：`#fff`
- 完成率颜色编码保持不变

## 视觉效果特点

1. **行级高亮**：整行背景高亮，从Y轴标签到图表右边界
2. **半透明背景**：使用 `rgba(64, 158, 255, 0.1)` 实现柔和的蓝色背景
3. **边框增强**：淡蓝色边框 `rgba(64, 158, 255, 0.3)` 增强视觉边界
4. **层级控制**：`z: 0` 确保高亮背景在柱子后面
5. **响应性**：300ms的动画过渡，提供流畅体验

## 交互行为

- **鼠标进入柱子**：当前行显示半透明蓝色背景高亮
- **鼠标移出柱子**：高亮背景消失
- **鼠标离开图表**：所有高亮效果清除
- **tooltip显示**：不受高亮效果影响，正常显示详细信息
- **柱子emphasis**：保持轻微的颜色变化效果

## 技术实现要点

1. **Graphic组件**：使用ECharts的graphic组件创建背景矩形
2. **动态计算位置**：根据grid区域和数据索引计算行位置
3. **事件监听**：监听mouseover、mouseout、globalout事件
4. **坐标转换**：处理ECharts Y轴从下往上的坐标系统
5. **性能优化**：只更新当前需要显示的graphic元素

## 核心方法

### showRowHighlight(dataIndex)
- 计算当前行的位置和尺寸
- 更新对应的graphic元素使其可见
- 设置正确的背景区域范围

### hideRowHighlight()
- 隐藏所有行高亮graphic元素
- 在鼠标离开时调用

### addMouseEvents()
- 绑定鼠标事件监听器
- 处理悬浮和离开事件
